"""
UI工具类
提供UI相关的工具类和组件
"""
from PySide6.QtCore import QTimer, QEvent, QPoint, QObject, Qt
from PySide6.QtWidgets import QLabel, QTableWidget
from PySide6.QtGui import QGuiApplication, QCursor, QColor


class HoverTableWidget(QTableWidget):
    """自定义表格控件，支持整行悬停高亮效果"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMouseTracking(True)
        self.hover_row = -1
        self.default_bg_colors = {}  # 存储默认背景色
        self.highlight_color = "#e7f5ff"  # 高亮颜色
    
    def leaveEvent(self, event):
        """鼠标离开表格时清除高亮"""
        self.clearRowHighlight()
        super().leaveEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动时更新高亮行"""
        row = self.rowAt(event.position().y())
        if row != self.hover_row:
            self.clearRowHighlight()
            if row >= 0:  # 有效行
                print(f"[DEBUG] 悬停到行 {row}，开始高亮整行")
                self.highlightRow(row)
            self.hover_row = row
        super().mouseMoveEvent(event)
    
    def highlightRow(self, row):
        """高亮整行"""
        print(f"[DEBUG] highlightRow: 开始高亮行 {row}，列数: {self.columnCount()}")
        for col in range(self.columnCount()):
            item = self.item(row, col)
            if item:
                # 保存原始背景色
                if row not in self.default_bg_colors:
                    self.default_bg_colors[row] = item.background()
                # 设置高亮
                item.setBackground(QColor(self.highlight_color))
                print(f"[DEBUG] 设置行 {row} 列 {col} 背景色为 {self.highlight_color}")
            else:
                print(f"[DEBUG] 行 {row} 列 {col} 的item为空")
    
    def clearRowHighlight(self):
        """清除高亮"""
        if self.hover_row >= 0:
            for col in range(self.columnCount()):
                item = self.item(self.hover_row, col)
                if item:
                    # 恢复原始背景色
                    if self.hover_row in self.default_bg_colors:
                        item.setBackground(self.default_bg_colors[self.hover_row])
                    else:
                        # 如果没有保存原始颜色，使用透明背景
                        item.setBackground(QColor(Qt.transparent))
            self.default_bg_colors.pop(self.hover_row, None)


class CustomToolTip(QObject):
    """自定义提示框工具类（修复位置问题）"""
    _current_tooltip = None
    
    @classmethod
    def install_tooltip(cls, widget, text, position="top", offset=10):
        """安装工具提示到控件"""
        return cls(widget, text, position, offset)
        
    def __init__(self, widget, text, position, offset):
        """初始化自定义工具提示"""
        super().__init__(widget)
        self.widget = widget
        self.text = text
        self.position = position
        self.offset = offset
        self.tooltip_label = None
        self.close_timer = None
        
        # 安装事件过滤器
        self.widget.installEventFilter(self)
        
    def eventFilter(self, obj, event):
        """事件过滤器处理鼠标事件"""
        if obj == self.widget:
            if event.type() == QEvent.Enter:
                self._show_tooltip()
            elif event.type() == QEvent.Leave:
                self._safe_hide_tooltip()
        return super().eventFilter(obj, event)
        
    def _show_tooltip(self):
        """显示工具提示"""
        # 如果已存在全局提示框，先隐藏
        if CustomToolTip._current_tooltip and CustomToolTip._current_tooltip != self:
            CustomToolTip._current_tooltip._safe_hide_tooltip()
            
        # 创建提示标签
        if not self.tooltip_label:
            self.tooltip_label = QLabel(self.text)
            self.tooltip_label.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
            self.tooltip_label.setStyleSheet("""
                background: #2c3e50;
                color: #ffffff;
                border: 1px solid #34495e;
                padding: 8px 12px;
                border-radius: 6px;
                font: 14px "Microsoft YaHei";
                border-bottom: 2px solid #3498db;
            """)
            
        # 更新位置
        self._update_tooltip_position()
        
        # 显示提示
        self.tooltip_label.show()
        CustomToolTip._current_tooltip = self
        
        # 设置自动关闭定时器
        self._start_close_timer()
        
    def _update_tooltip_position(self):
        """更新工具提示位置"""
        if not self.tooltip_label:
            return
            
        # 获取控件的全局位置
        widget_rect = self.widget.rect()
        global_pos = self.widget.mapToGlobal(QPoint(0, 0))
        
        # 根据位置参数计算提示框位置
        if self.position == "top":
            x = global_pos.x() + widget_rect.width() // 2 - self.tooltip_label.width() // 2
            y = global_pos.y() - self.tooltip_label.height() - self.offset
        elif self.position == "bottom":
            x = global_pos.x() + widget_rect.width() // 2 - self.tooltip_label.width() // 2
            y = global_pos.y() + widget_rect.height() + self.offset
        elif self.position == "left":
            x = global_pos.x() - self.tooltip_label.width() - self.offset
            y = global_pos.y() + widget_rect.height() // 2 - self.tooltip_label.height() // 2
        else:  # right
            x = global_pos.x() + widget_rect.width() + self.offset
            y = global_pos.y() + widget_rect.height() // 2 - self.tooltip_label.height() // 2
            
        # 确保提示框在屏幕内
        screen = QGuiApplication.screenAt(QCursor.pos())
        if screen:
            screen_geometry = screen.geometry()
            x = max(screen_geometry.left(), min(x, screen_geometry.right() - self.tooltip_label.width()))
            y = max(screen_geometry.top(), min(y, screen_geometry.bottom() - self.tooltip_label.height()))
            
        self.tooltip_label.move(x, y)
        
    def _start_close_timer(self):
        """启动关闭定时器"""
        if self.close_timer:
            self.close_timer.stop()
        self.close_timer = QTimer()
        self.close_timer.setSingleShot(True)
        self.close_timer.timeout.connect(self._safe_hide_tooltip)
        self.close_timer.start(3000)  # 3秒后自动关闭
        
    def _safe_hide_tooltip(self):
        """安全隐藏工具提示"""
        if self.tooltip_label:
            self.tooltip_label.hide()
            if CustomToolTip._current_tooltip == self:
                CustomToolTip._current_tooltip = None 