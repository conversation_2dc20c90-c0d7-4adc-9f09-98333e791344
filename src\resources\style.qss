/* ================= 全局基础设置 ================= */
/* 设置所有QWidget的默认字体、字号和颜色 */
QWidget {
    font-family: "Microsoft YaHei UI";
    font-size: 14px;
    color: #1a1a1a;
}

/* ================= 主窗体头部样式 ================= */
/* 主窗口背景色 - 使用浅灰背景避免黑边 */
QMainWindow {
    background-color: #f8f9fa;
    border-radius: 12px;
}

/* 最大化状态下的主窗口 - 移除圆角 */
QMainWindow[maximized="true"] {
    border-radius: 0px;
}

/* 中央部件样式 - 使用浅灰背景 */
QMainWindow QWidget#central_widget {
    background-color: #f8f9fa;
    border: none;
}

/* 最大化状态下的中央部件 */
QMainWindow[maximized="true"] QWidget#central_widget {
    background-color: #f8f9fa;
    border: none;
}

/* 内容容器样式 - 完全填充，无边框避免黑边 */
QWidget#content_container {
    border-radius: 12px;
    background-color: #f8f9fa;
    border: none;
}

/* 最大化状态下的内容容器 - 移除圆角和边框 */
QMainWindow[maximized="true"] QWidget#content_container {
    border-radius: 0px;
    border: none;
}

/* 品牌标签样式 */
QLabel#brandLabel {
    font-size: 16px;
    font-weight: bold;
    color:#1A91EB;
}

/* 顶部工具栏样式 - 现代化渐变设计，贴合窗体边缘 */
#mainTopBar {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1e88e5, stop:0.5 #2196f3, stop:1 #42a5f5) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    min-height: 60px;
    margin: 0px;
    padding: 0px;
}

/* TopBar内所有组件强制透明背景 */
#mainTopBar * {
    background: transparent !important;
    background-color: transparent !important;
}

/* TopBar内所有标签样式 */
#mainTopBar QLabel {
    background: transparent !important;
    background-color: transparent !important;
    color: #ffffff !important;
    font-weight: bold !important;
    border: none !important;
    padding: 0px !important;
    margin: 0px !important;
}

/* 品牌标签特殊处理 */
#mainTopBar QLabel#brandLabel {
    background: transparent !important;
    background-color: transparent !important;
    color: #ffffff !important;
    font-weight: bold !important;
    border: none !important;
    padding: 0px !important;
    margin: 0px !important;
}

/* 窗口控制按钮样式 - 现代化设计 */
QPushButton#windowControlBtn {
    color: #ffffff;
    min-width: 32px;
    max-width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    font-size: 16px;
    font-weight: bold;
    border-radius: 6px;
}

/* 窗口控制按钮悬停效果 */
QPushButton#windowControlBtn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 6px;
}

/* 窗口控制按钮按下效果 */
QPushButton#windowControlBtn:pressed {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
}

/* 汉堡菜单按钮样式 - 现代化设计 */
QPushButton#menuBtn {
    color: #ffffff;
    min-width: 20px;
    max-width: 20px;
    height: 20px;
    font-size: 18px;
    font-weight: bold;
}

/* 汉堡菜单按钮悬停效果 */
QPushButton#menuBtn:hover {
    background: rgba(255, 255, 255, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

/* 汉堡菜单按钮按下效果 */
QPushButton#menuBtn:pressed {
    background: rgba(255, 255, 255, 0.35);
    border: 1px solid rgba(255, 255, 255, 0.7);
}




/* ================= 状态栏样式 ================= */
QStatusBar {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    color: #6c757d;
    font-size: 12px;
    padding: 0 16px;
}

/* 主状态栏样式 - 加深颜色与内容区区分 */
QStatusBar#mainStatusBar {
    background-color: #e9ecef;
    border-top: 2px solid #adb5bd;
    color: #495057;
    font-size: 12px;
    padding: 8px 16px;
    min-height: 32px;
}

/* 状态栏子项样式 */
QStatusBar::item {
    border: none;  /* 移除子控件边框 */
}

/* 状态栏版本标签样式 */
QLabel#versionLabel {
    color: #495057;
    font-weight: 500;
    padding: 4px 8px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    margin-right: 8px;
}

/* 状态栏网站标签样式 */
QLabel#websiteLabel {
    color: #007bff;
    font-weight: 500;
    padding: 4px 8px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    text-decoration: underline;
}

/* 状态栏网站标签悬停效果 */
QLabel#websiteLabel:hover {
    color: #0056b3;
    background-color: rgba(255, 255, 255, 0.9);
}

/* ================= 增强型状态栏样式修复 ================= */
/* EnhancedStatusBar主容器 - 设置为浅灰色背景与主窗口一致 */
QWidget#enhancedStatusBar {
    background-color: #F2F3F5 !important;
    border-top: 2px solid #495057 !important;
    min-height: 38px !important;
    padding: 0px !important;
}

/* EnhancedStatusBar内所有组件强制透明背景 */
QWidget#enhancedStatusBar * {
    background: transparent !important;
    background-color: transparent !important;
}

/* EnhancedStatusBar内所有标签样式 */
QWidget#enhancedStatusBar QLabel {
    background: transparent !important;
    background-color: transparent !important;
    border: none !important;
    padding: 4px 8px !important;
    margin: 0px !important;
}

/* 状态消息标签特殊处理 - 深色文字在浅灰色背景上 */
QWidget#enhancedStatusBar QLabel#statusMessage {
    background: transparent !important;
    background-color: transparent !important;
    color: #495057 !important;
    font-weight: normal !important;
}

/* 版本标签特殊处理 - 在灰色背景上的白色标签（支持富文本） */
QWidget#enhancedStatusBar QLabel#versionLabel {
    background: rgba(255, 255, 255, 0.9) !important;
    color: #343a40 !important;
    font-weight: 500 !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;  /* 统一padding，无间距问题 */
}

/* 网站标签特殊处理 - 在灰色背景上的蓝色链接 */
QWidget#enhancedStatusBar QLabel#websiteLabel {
    background: rgba(255, 255, 255, 0.9) !important;
    color: #007bff !important;
    font-weight: 500 !important;
    border-radius: 4px !important;
    text-decoration: underline !important;
}

/* 网站标签悬停效果 */
QWidget#enhancedStatusBar QLabel#websiteLabel:hover {
    background: rgba(255, 255, 255, 1.0) !important;
    color: #0056b3 !important;
}

/* 版本容器样式 */
QWidget#versionContainer {
    background: transparent !important;
    background-color: transparent !important;
}

/* 更新提示小球样式已整合到版本标签中，使用富文本实现 */
/* 版本标签悬停效果（当有更新时） */
QWidget#enhancedStatusBar QLabel#versionLabel:hover {
    background: rgba(255, 255, 255, 1.0) !important;
}

/* ================= 导航系统样式 ================= */
QListWidget {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
    padding: 12px 8px;
    outline: none;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    /* 使用多层边框模拟阴影效果，与LeftPanel/RightPanel保持一致 */
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
}

/* 导航项样式 */
QListWidget::item {
    height: 48px;
    padding-left: 12px;  /* 从16px减少到12px，为文字提供更多空间 */
    padding-right: 8px;  /* 添加右内边距，确保文字不贴边 */
    font-size: 14px;
    font-weight: 500;
    border-radius: 8px;
    margin: 3px 2px;  /* 从4px减少到2px，减少左右边距 */
    border: none;
    outline: none;
    color: #495057;
}

/* 导航项悬停效果 */
QListWidget::item:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #1976d2;
    font-weight: 600;
}

/* 导航项选中效果 */
QListWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #1976d2, stop:0.5 #2196f3, stop:1 #1565c0);
    color: #ffffff;
    font-weight: bold;
    border: none;
    outline: none;
    border-left: 3px solid #0d47a1;  /* 从4px减少到3px，节省空间 */
    padding-left: 10px;  /* 选中时减少左内边距，补偿边框占用的空间 */
}


/* 右侧面板整体背景 */
QWidget#rightPanel {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #fafafa);
    border-radius: 12px;
    border: 1px solid #e9ecef;
    /* 使用多层边框模拟阴影效果，与LeftPanel保持一致 */
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    /* 参考Function List的边距设置：功能区右边距6px + 右内边距8px = 14px */
    /* 保持与Function List对窗体左边距的一致间距 */
    padding: 12px 8px;
}

/* ================= 左侧面板样式 ================= */
QWidget#LeftPanel {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #fafafa);
    border-radius: 12px;
    border: 1px solid #e9ecef;
    /* 使用多层边框模拟阴影效果 */
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    /* 参考Function List的内边距设置，保持与TopBar的完全一致间距 */
    padding: 12px 8px;
    /* 确保与Function List相同的与TopBar总间距：6px(功能区边距) + 12px(上内边距) = 18px */
}

/* ================= 功能标题样式 ================= */
QLabel#functionTitle, #basicTitle {
    font-size: 15px;  /* 从16px减少到15px */
    font-weight: 700;
    color: #2c3e50;
    margin-right: 12px;  /* 从20px减少到12px */
    padding-left: 8px;   /* 从12px减少到8px */
    min-width: 100px;    /* 从120px减少到100px */
    min-height: 24px;    /* 从28px减少到24px */
    border-left: 3px solid #3498db;  /* 从4px减少到3px */
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(52, 152, 219, 0.1), stop:1 transparent) !important;
    border-radius: 4px;
}

/* ================= 标签页样式 ================= */
QTabWidget::pane {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-top: 2px;  /* 从4px减少到2px */
}

/* 标签页标签样式 */
QTabBar::tab {
    padding: 6px 8px;  /* 从8px 10px减少到6px 8px */
    background: #e9ecef;
    color: #495057;
}

/* 标签页选中状态 */
QTabBar::tab:selected {
    background: #1A91EB;
    color: #FFFFFF;
    border-bottom: 2px solid #FFFFFF;
}

/* ================= 表单控件样式 ================= */
/* 输入框、文本编辑框、下拉框通用样式 */
QLineEdit, QTextEdit, QComboBox {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 6px 8px;  /* 从10px 12px减少到6px 8px */
    background: #ffffff;
    min-height: 20px;  /* 从24px减少到20px */
    font-size: 14px;
    color: #495057;
}

/* 输入框悬停效果 */
QLineEdit:hover, QTextEdit:hover, QComboBox:hover {
    border-color: #2196f3;
    background: #f8f9fa;
}

/* 输入框焦点效果 */
QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
    border-color: #1976d2;
    background: #ffffff;
    outline: none;
}


/* ================= 数字输入框样式 ================= */
QSpinBox {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 6px 32px 6px 12px;  /* 右侧留出空间给按钮，减少上下内边距 */
    font-size: 14px;
    color: #495057;
    min-height: 18px;  /* 从22px减少到18px */
    font-weight: 500;
}

/* 数字输入框悬停效果 */
QSpinBox:hover {

    color: #495057;
}



/* 数字输入框禁用状态 */
QSpinBox:disabled {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
    color: #6c757d;
    border-color: #dee2e6;
}

/* 数字输入框上按钮 */
QSpinBox::up-button {
    subcontrol-origin: border;
    subcontrol-position: top right;
    width: 20px;
    height: 12px;  /* 从14px减少到12px */
    border: 1px solid #e9ecef;
    border-left: none;
    border-bottom: none;
    border-top-right-radius: 8px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
    margin: 1px;
}

/* 数字输入框上按钮悬停时显示手势光标 */
QSpinBox::up-button:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e3f2fd, stop:1 #bbdefb);
    border-color: #2196f3;
}

/* 数字输入框下按钮 */
QSpinBox::down-button {
    subcontrol-origin: border;
    subcontrol-position: bottom right;
    width: 20px;
    height: 12px;  /* 从14px减少到12px */
    border: 1px solid #e9ecef;
    border-left: none;
    border-top: none;
    border-bottom-right-radius: 8px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #ffffff);
    margin: 1px;
}

/* 数字输入框下按钮悬停时显示手势光标 */
QSpinBox::down-button:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e3f2fd, stop:1 #bbdefb);
    border-color: #2196f3;
}

/* 禁用状态的按钮 */
QSpinBox:disabled::up-button, QSpinBox:disabled::down-button {
    background: #f8f9fa;
    border-color: #dee2e6;
}

/* 数字输入框上箭头 - 备用方案：CSS三角形 */
QSpinBox::up-arrow {
    /* 首先尝试SVG图标 */
    image: url(up-svgrepo.svg);
    width: 16px;
    height: 16px;
    margin: 1px;
    /* 如果SVG不显示，使用CSS三角形作为备用 */
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;

}

/* 数字输入框下箭头 - 备用方案：CSS三角形 */
QSpinBox::down-arrow {
    /* 首先尝试SVG图标 */
    image: url(down-svgrepo.svg);
    width: 16px;
    height: 16px;
    margin: 1px;
    /* 如果SVG不显示，使用CSS三角形作为备用 */
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;

}

/* 箭头悬停时变色 - 使用蓝色SVG */
QSpinBox::up-button:hover QSpinBox::up-arrow {
    image: url(up-svgrepo-hover.svg);
}

QSpinBox::down-button:hover QSpinBox::down-arrow {
    image: url(down-svgrepo-hover.svg);
}

/* 箭头按下时变色 - 使用白色SVG */
QSpinBox::up-button:pressed QSpinBox::up-arrow {
    image: url(up-svgrepo-pressed.svg);
}

QSpinBox::down-button:pressed QSpinBox::down-arrow {
    image: url(down-svgrepo-pressed.svg);
}

/* 禁用状态的箭头 - 使用浅灰色SVG */
QSpinBox:disabled::up-arrow {
    image: url(up-svgrepo-disabled.svg);
}

QSpinBox:disabled::down-arrow {
    image: url(down-svgrepo-disabled.svg);
}

/* 数字输入框上按钮按下效果 */
QSpinBox::up-button:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1976d2, stop:1 #1565c0);
    border-color: #1565c0;
}

/* 数字输入框下按钮按下效果 */
QSpinBox::down-button:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1976d2, stop:1 #1565c0);
    border-color: #1565c0;
}



/* ================= 按钮样式 ================= */
/* 成功按钮样式 */
#successBtn {
    background-color: #1A91EB;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 18px;  /* 从10px 24px减少到8px 18px */
    font-size: 15px;    /* 从16px减少到15px */
    font-weight: 500;
}

/* 成功按钮悬停效果 */
#successBtn:hover {
    background-color: #1A91EB;
}

/* 成功按钮按下效果 */
#successBtn:pressed {
    background-color: #1A91EB;
}

/* 辅助按钮通用样式 */
#warningBtn, #primaryBtn, #dangerBtn {
    background-color: #e7e7e7;
    color: black;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;  /* 从10px 14px减少到8px 12px */
    font-size: 14px;
    font-weight: 500;
}

/* 警告按钮悬停效果 */
#warningBtn:hover {
    background-color: #FFFFFF;
}

/* 主要按钮悬停效果 */
#primaryBtn:hover {
    background-color: #1A91EB;
}

/* 危险按钮悬停效果 */
#dangerBtn:hover {
    background-color: #f44336;
}

/* 警告按钮按下效果 */
#warningBtn:pressed {
    background-color: #1A91EB;
}

/* 主要按钮按下效果 */
#primaryBtn:pressed {
    background-color: #FFFFFF;
}

/* 危险按钮按下效果 */
#dangerBtn:pressed {
    background-color: #FFFFFF;
}

/* ================= 表格相关样式 ================= */
/* 表头样式 */
QHeaderView::section {
    background-color: #FFFFFF;
    color: #32373F;
    padding: 8px 12px;  /* 从12px 16px减少到8px 12px */
    border: none;
    font-size: 13px;
    font-weight: 600;
}

/* 表格基础样式 */
QTableWidget {
    min-width: 120px;
    font-size: 14px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #ffffff;
    qproperty-showGrid: false;
    outline: none;
    /* 添加轻微的阴影效果 */
    border-bottom: 2px solid rgba(0, 0, 0, 0.03);
}

/* 表格项焦点样式 */
QTableWidget::item:focus {
    border: none;
    outline: none;
}

/* 表格项悬停效果 - 修改为整行高亮 */
QTableWidget {
    selection-background-color: #e7f5ff;  /* 悬停行颜色改为淡蓝色 */
    selection-color: #000000;  /* 选中行文字颜色改为黑色 */
    show-decoration-selected: 1;  /* 显示整行选择 */
}

/* 表格项悬停效果 */
QTableWidget::item {
    border: none;  /* 确保所有单元格无边框 */
}

/* 表格项悬停效果 - 禁用单元格悬停，改为整行悬停 */
/* QTableWidget::item:hover {
    background-color: #e7f5ff;
    border: none;
} */

/* 尝试实现整行高亮效果 */
QTableWidget {
    selection-background-color: #e7f5ff;
    selection-color: #000000;
}

/* 当鼠标悬停在表格上时，整行高亮 */
QTableWidget:hover {
    selection-background-color: #e7f5ff;
}

/* 表格选中项样式 - 用于整行高亮 */
QTableWidget::item:selected {
    background-color: #e7f5ff !important;
    color: #000000 !important;
    border: none;
    outline: none;
}

/* 表格状态颜色 */
QTableWidget::item[status="等待处理"] { color: #868e96; }
QTableWidget::item[status*="成功"]   { color: #2b8a3e; }
QTableWidget::item[status*="失败"]   { color: #c92a2a; }

/* 表格操作按钮样式 */
#btndelete, #btnopen, #sortBtn {
    background-color: transparent;
    border: none;  /* 移除边框 */
    min-height: 30px; max-height: 30px;
    min-width:30px;max-width:30px;
    font-weight: 500;
    color: #a3a3a3;
    border-radius: 4px;  /* 添加圆角 */
}

/* 表格操作按钮悬停效果 */
#btndelete:hover, #btnopen:hover, #sortBtn:hover {
    background-color: #e7f5ff !important;  /* 淡蓝色背景 */
    color: #1A91EB !important;  /* 蓝色图标 */
    border: 1px solid #b3d9ff;  /* 淡蓝色边框 */
}

/* 表格操作按钮按下效果 */
#btndelete:pressed, #btnopen:pressed, #sortBtn:pressed {
    background-color: #cce7ff !important;  /* 更深的蓝色背景 */
    color: #0066cc !important;  /* 更深的蓝色图标 */
}

/* 删除按钮特殊悬停效果 */
#btndelete:hover {
    background-color: #ffe7e7 !important;  /* 淡红色背景 */
    color: #dc3545 !important;  /* 红色图标 */
    border: 1px solid #ffb3b3;  /* 淡红色边框 */
}

/* ================= 工具提示样式 ================= */
QToolTip {
    background: #2c3e50;
    color: #ffffff;
    border: 1px solid #34495e;
    padding: 8px 12px;
    border-radius: 6px;
    font: 14px "Microsoft YaHei";
    border-bottom: 2px solid #3498db;
}

/* 成功提示样式 */
QToolTip[type="success"] {
    background: #27ae60;
    border: 1px solid #2ecc71;
    border-bottom: 2px solid #2ecc71;
}

/* 警告提示样式 */
QToolTip[type="warning"] {
    background: #f39c12;
    border: 1px solid #e67e22;
    border-bottom: 2px solid #e67e22;
}

/* 错误提示样式 */
QToolTip[type="error"] {
    background: #e74c3c;
    border: 1px solid #c0392b;
    border-bottom: 2px solid #c0392b;
}

/* 信息提示样式 */
QToolTip[type="info"] {
    background: #3498db;
    border: 1px solid #2980b9;
    border-bottom: 2px solid #2980b9;
}

/* ================= 对话框样式 ================= */
QDialog {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
}

/* 对话框标签样式 */
QLabel {
    font-size: 14px;
    margin: 6px 0;  /* 从10px 0减少到6px 0 */
}

/* 对话框危险按钮样式 */
QPushButton#dangerBtn {
    background: #ff4444;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
}

/* 对话框危险按钮悬停效果 */
QPushButton#dangerBtn:hover {
    background: #cc0000;
}


/* ================= 登录窗口样式 ================= */
/* 登录窗口主容器 */
QDialog#LoginWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
    border-radius: 16px;
    border: 1px solid #e9ecef;
    /* box-shadow 不被Qt支持，使用border模拟阴影效果 */
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
}

/* 登录窗口关闭按钮 */
QPushButton#closeBtn {
    background: transparent;
    border: none;
    color: #6c757d;
    font-size: 20px;
    font-weight: bold;
    min-width: 30px;
    max-width: 30px;
    min-height: 30px;
    max-height: 30px;
    border-radius: 15px;
}

QPushButton#closeBtn:hover {
    background: #f8f9fa;
    color: #dc3545;
}

QPushButton#closeBtn:pressed {
    background: #e9ecef;
}

/* 登录方式切换标签按钮 */
QPushButton#tabBtn {
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-size: 16px;
    font-weight: 600;
    padding: 12px 24px;
    min-height: 40px;
}

QPushButton#tabBtn:hover {
    color: #495057;
    background: rgba(33, 150, 243, 0.05);
}

QPushButton#tabBtn:checked {
    color: #2196f3;
    border-bottom: 3px solid #2196f3;
    background: rgba(33, 150, 243, 0.1);
}

/* 登录输入框样式 */
QLineEdit#loginInput {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    color: #495057;
    min-height: 20px;
}

QLineEdit#loginInput:focus {
    border-color: #2196f3;
    background: #ffffff;
    outline: none;
}

QLineEdit#loginInput:hover {
    border-color: #adb5bd;
}

/* 验证码获取按钮 */
QPushButton#code_btn {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #42a5f5, stop:1 #2196f3);
    border: none;
    border-radius: 6px;
    color: #ffffff;
    font-size: 13px;
    font-weight: 600;
    padding: 10px 16px;
    min-width: 100px;
}

QPushButton#code_btn:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1e88e5, stop:1 #1976d2);
}

QPushButton#code_btn:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #1565c0, stop:1 #0d47a1);
}

QPushButton#code_btn:disabled {
    background: #adb5bd;
    color: #ffffff;
}

/* 登录按钮 */
QPushButton#loginBtn {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4caf50, stop:1 #388e3c);
    border: none;
    border-radius: 8px;
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    padding: 14px 24px;
    min-height: 20px;
}

QPushButton#loginBtn:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #66bb6a, stop:1 #4caf50);
}

QPushButton#loginBtn:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #2e7d32, stop:1 #1b5e20);
}

QPushButton#loginBtn:disabled {
    background: #adb5bd;
    color: #ffffff;
}

/* 动作按钮样式 (顶部栏按钮) */
QPushButton#actionBtn {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: #ffffff;
    font-size: 13px;
    font-weight: 600;
    padding: 8px 16px;
    min-height: 16px;
}

QPushButton#actionBtn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
}

QPushButton#actionBtn:pressed {
    background: rgba(255, 255, 255, 0.35);
    border-color: rgba(255, 255, 255, 0.7);
}

/* 错误提示标签 */
QLabel#error-label {
    background: #fee2e2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    color: #dc2626;
    font-size: 13px;
    padding: 8px 12px;
    margin: 4px 0;
}

/* 成功提示标签 */
QLabel#success-label {
    background: #dcfce7;
    border: 1px solid #bbf7d0;
    border-radius: 6px;
    color: #16a34a;
    font-size: 13px;
    padding: 8px 12px;
    margin: 4px 0;
}

/* 二维码提示标签 */
QLabel#qrcode_tip {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    margin: 8px 0;
}

/* 复选框样式 - 已恢复为系统默认样式 */

/* 状态标签样式 */
#statusLabel {
    border-radius: 4px;
    padding: 4px 8px;
    color: #333;
    margin: 4px;
}
